"use client";

import { useState, useEffect, useContext } from "react";
import { LoadingContext } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface ProductionStats {
  orders: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
    blocked: number;
  };
  colis: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
  };
  packets: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
    blocked: number;
  };
  pieces: {
    total: number;
    pending: number;
    in_progress: number;
    finishing: number;
    completed: number;
  };
  productivity: {
    completionRate: number;
    averageTimePerOrder: number;
    totalScans: number;
  };
}

interface UseGetProductionStatsParams {
  startDate?: string;
  endDate?: string;
}

export const useGetProductionStats = ({ startDate, endDate }: UseGetProductionStatsParams = {}) => {
  const [stats, setStats] = useState<ProductionStats | null>(null);
  const [loading2, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useContext(LoadingContext);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading2(true);
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);

        const response = await axiosInstance.get(`/api/stats/production?${params.toString()}`);
        setStats(response.data);
      } catch (err: any) {
        console.error("Error fetching production stats:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des statistiques");
      } finally {
        setLoading2(false);
        setLoading(false);
      }
    };

    fetchStats();
  }, [startDate, endDate, setLoading]);

  return { stats, loading: loading2, error };
};
