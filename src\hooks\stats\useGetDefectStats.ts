"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface DefectStats {
  totalPacketsWithDefects: number;
  totalPiecesWithDefects: number;
  packetDefects: Record<string, number>;
  pieceDefects: Record<string, number>;
  colisProblems: Record<string, number>;
  retouchePackets: number;
  blockedOrders: number;
  blockedPackets: number;
}

interface UseGetDefectStatsParams {
  startDate?: string;
  endDate?: string;
}

export const useGetDefectStats = ({ startDate, endDate }: UseGetDefectStatsParams = {}) => {
  const [stats, setStats] = useState<DefectStats | null>(null);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);

        const response = await axiosInstance.get(`/stats/defects?${params.toString()}`);
        setStats(response.data);
      } catch (err: any) {
        console.error("Error fetching defect stats:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des statistiques de défauts");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchStats();
  }, [startDate, endDate, setLoading]);

  return { stats, loading, error };
};
