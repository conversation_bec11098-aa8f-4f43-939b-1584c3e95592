"use client";

import { useMemo } from "react";
import { Col<PERSON> } from "@/types/models";
import { Package, Boxes, ArrowLeft, Siren, Dot, ScanBarcode, Palette, Shield, AlertTriangle, Lock, AlertCircle } from "lucide-react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import Bar from "@/components/Barcode/BarCode";
import Link from "next/link";
import BarcodeSaveButton from "@/components/Barcode/BarcodeSaveButton";

interface Props {
  colis: Colis;
}

 const getStatusLabel = (status: string) => {
    switch (status) {
      case "pending":
        return { text: "En attente", color: "#FFA70B" };
      case "in_progress":
        return { text: "En cours", color: "#2D9CDB" };
      case "finnishing":
        return { text: "Finitions", color: "#9B51E0" };
      case "completed":
        return { text: "Terminée", color: "#219653" };
      case "faulted":
        return { text: "En défaut", color: "#f60303" };
      case "retouche":
        return { text: "Retouche", color: "#f60303" };
      case "canceled":
        return { text: "Annulée", color: "#D34053" };
      default:
        return { text: "Inconnu", color: "#6B7280" };
    }
  };

  const getControlLabel = (control: string) => {
    switch (control) {
      case "Conforme":
        return { text: "Conforme", color: "#219653", icon: Shield };
      case "ANC":
        return { text: "ANC", color: "#FFA70B", icon: AlertTriangle };
      case "Bloque":
        return { text: "Bloqué", color: "#f60303", icon: Lock };
      default:
        return { text: "Non contrôlé", color: "#6B7280", icon: Shield };
    }
  };

export default function ColisDetails({ colis }: Props) {

  const { text, color } = getStatusLabel(colis.status);

  // Calculate total pieces
  const totalPieces = colis.quantite;

  // Generate packet status summary
  const packetStatusSummary = useMemo(() => {
    const summary: Record<string, number> = {};
    colis.packets.forEach((packet: any) => {
      summary[packet.status] = (summary[packet.status] || 0) + 1;
    });
    return summary;
  }, [colis.packets]);

  // Get order info
  const order = typeof colis.order === 'object' ? colis.order : null;
  const orderNumber = order?.orderNumber || 'N/A';

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center gap-2">
        <Link href={`/orders/${order?._id}`} className="text-blue-600 hover:text-blue-800">
          <ArrowLeft size={20} />
        </Link>

      </div>
<Breadcrumb pageName={`Colis N°${colis.numeroColis} - OF ${orderNumber}`} />
      {/* Colis Info Header */}
      <div className="flex justify-between pr-8 pl-8 pt-2 pb-2 rounded-lg items-center bg-white border border-blue-950 flex-wrap">
        <p className="font-semibold flex justify-center items-center">
          <Palette size={25} className="mr-1" />
          <span>{colis.coloris}</span>
        </p>

        <p className="font-semibold flex justify-center items-center">
          <Package size={25} className="mr-1" />
          <span>{colis.tailles}</span>
        </p>

        <p className="font-semibold flex justify-center items-center">
          <Boxes size={25} className="mr-1" />
          <span>Total pièces: {totalPieces}</span>
        </p>

        <div className="flex max-w-fit items-center justify-center rounded-full px-3.5 py-1 font-bold">
          <ScanBarcode size={35} className="mb-1" />
          <div className="pt-2 max-w-[150px]">
            <Bar value={colis.qrCode} size="medium" />
          </div>
        </div>

        <div
          className="max-w-fit rounded-full px-3.5 py-1 font-bold flex justify-center items-center"
          style={{
            backgroundColor: `${color}14`,
            color: color,
          }}
        >
          <Siren style={{ color: color }} size={26} className="mb-1" />
          <span>{text}</span>
        </div>

        {/* Control Status */}
        {colis.control && (() => {
          const controlInfo = getControlLabel(colis.control);
          const ControlIcon = controlInfo.icon;
          return (
            <div
              className="max-w-fit rounded-full px-3.5 py-1 font-bold flex justify-center items-center"
              style={{
                backgroundColor: `${controlInfo.color}14`,
                color: controlInfo.color,
              }}
            >
              <ControlIcon style={{ color: controlInfo.color }} size={26} className="mb-1 mr-1" />
              <span>{controlInfo.text}</span>
            </div>
          );
        })()}
      </div>

      {/* Problems Section */}
      {colis.problems && colis.problems.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-4">
          <div className="flex items-center mb-2">
            <AlertCircle className="text-red-600 mr-2" size={20} />
            <h3 className="font-semibold text-red-800">Problèmes détectés</h3>
          </div>
          <ul className="list-disc pl-6 space-y-1">
            {colis.problems.map((problem: string, index: number) => (
              <li key={index} className="text-red-700">{problem}</li>
            ))}
          </ul>
        </div>
      )}



      {/* Status Summary */}
      <div className="flex flex-wrap gap-8 mt-4 bg-white rounded-lg p-2 border border-blue-950">
        {Object.entries(packetStatusSummary).map(([status, count]) => {
          const { color, text } = getStatusLabel(status);
          return (
            <div
              key={status}
              className="flex items-center border pt-1 pb-1 pl-1 pr-4 rounded-full bg-white"
              style={{ borderColor: color }}
            >
              <Dot fill={color} color={color} size={35}/>
              <span className="font-semibold" style={{ color }}>
                {count} {text}
              </span>
            </div>
          );
        })}

        <div className="flex justify-end items-center ml-2">
          <BarcodeSaveButton
            BtnText={"Barcodes Packets"}
            Type={"PACKET"}
            QrCodes={colis.packets.map((packet: any) => ({
              qrValue: packet.qrCode,
              qrQuantity: packet.pieces.length,
            }))}
          />
        </div>
      </div>

      {/* Packets Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 mt-6">
          {colis.packets.map((packet: any) => {
            const { color } = getStatusLabel(packet.status);

            // Find debutGM and the latest ctrlFinCh scan
            const debutGM = packet.scans.find((s: any) => s.type === "debutGM");

            // Find all ctrlFinCh scans and get the latest one
            const ctrlFinChScans = packet.scans.filter((s: any) => s.type === "ctrlFinCh");
            const latestCtrlFinCh = ctrlFinChScans.length > 0
              ? ctrlFinChScans.reduce((latest: any, current: any) => {
                  return new Date(current.time) > new Date(latest.time) ? current : latest;
                }, ctrlFinChScans[0])
              : null;

            let durationDisplay = null;
            // Only display duration when packet status is not "in_progress"
            if (debutGM && latestCtrlFinCh && packet.status !== "in_progress" && packet.status !== "retouche") {
              const durationMs =
                new Date(latestCtrlFinCh.time).getTime() - new Date(debutGM.time).getTime();
              const totalSeconds = Math.floor(durationMs / 1000);
              const hours = Math.floor(totalSeconds / 3600);
              const minutes = Math.floor((totalSeconds % 3600) / 60);
              const seconds = totalSeconds % 60;

              durationDisplay = (
                <p className="text-center text-green-700 font-medium pb-2">
                  Terminé en {hours}h {minutes}m {seconds}s
                </p>
              );
            }

            return (
              <Link key={packet._id} href={`/packets/${packet._id}`} passHref>
                <div
                  className="border rounded-xl p-4 shadow-md bg-white hover:shadow-lg transition-shadow"
                  style={{ borderColor: color }}
                >
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex flex-col gap-1">
                      <div
                        className="text-sm px-3 py-1 rounded-full font-medium"
                        style={{ backgroundColor: `${color}14`, color }}
                      >
                        {getStatusLabel(packet.status).text}
                      </div>
                      {/* Blocked Status */}
                      {packet.bloquer && (
                        <div className="text-xs px-2 py-1 rounded-full font-medium flex items-center gap-1 bg-red-100 text-red-900">
                          <Lock size={12} />
                          Bloqué
                        </div>
                      )}
                    </div>
                    <div className="text-sm font-medium">
                      N°{packet.numero}
                    </div>
                  </div>

                  {/* ⏱ Terminé en ... */}
                  {durationDisplay}

                  <div className="flex justify-center mb-3">
                    <div className="max-w-[120px]">
                      <Bar value={packet.qrCode} size="small" />
                    </div>
                  </div>

                  <div className="text-center mb-3">
                    <h3 className="font-bold text-lg text-gray-800">Paquet {packet.numero}</h3>
                    <div className="flex justify-center items-center gap-2 text-gray-600">
                      <Boxes size={18} />
                      <span>{packet.pieces.length} pièces</span>
                    </div>
                  </div>
                </div>
              </Link>
            );
          })}
      </div>
    </div>
  );
}
