"use client";

import { cn } from "@/lib/utils";
import { useGetOperationsStats } from "@/hooks/stats/useGetOperationsStats";
import { PeriodPicker } from "@/components/period-picker";

type PropsType = {
  timeFrame?: string;
  className?: string;
};

export function OperationsPerformance({
  timeFrame = "monthly",
  className,
}: PropsType) {
  const currentDate = new Date();
  let startDate: string, endDate: string;

  if (timeFrame === "weekly") {
    const weekAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    startDate = weekAgo.toISOString().split('T')[0];
    endDate = currentDate.toISOString().split('T')[0];
  } else if (timeFrame === "yearly") {
    startDate = new Date(currentDate.getFullYear(), 0, 1).toISOString().split('T')[0];
    endDate = new Date(currentDate.getFullYear(), 11, 31).toISOString().split('T')[0];
  } else {
    startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).toISOString().split('T')[0];
    endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).toISOString().split('T')[0];
  }

  const { stats, loading, error } = useGetOperationsStats({ startDate, endDate });

  if (loading) {
    return (
      <div
        className={cn(
          "rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Performance des Opérations
          </h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="operations_performance" />
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div
        className={cn(
          "rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Performance des Opérations
          </h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="operations_performance" />
        </div>
        <div className="flex items-center justify-center h-64 text-red-500">
          Erreur lors du chargement des données
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex flex-wrap items-center justify-between gap-4 mb-6">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          Performance des Opérations
        </h2>
        <PeriodPicker defaultValue={timeFrame} sectionKey="operations_performance" />
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-stroke dark:border-dark-3">
              <th className="text-left py-3 px-2 font-medium text-dark dark:text-white">
                Opération
              </th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">
                Total Scans
              </th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">
                Paquets Uniques
              </th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">
                Utilisateurs
              </th>
              <th className="text-center py-3 px-2 font-medium text-dark dark:text-white">
                Pourcentage
              </th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(stats) && stats.slice(0, 10).map((operation, index) => (
              <tr
                key={operation.name}
                className="border-b border-stroke dark:border-dark-3 hover:bg-gray-1 dark:hover:bg-dark-2"
              >
                <td className="py-3 px-2">
                  <div className="flex items-center gap-3">
                    <div className="flex-shrink-0 w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                      <span className="text-primary font-medium text-sm">
                        {index + 1}
                      </span>
                    </div>
                    <span className="font-medium text-dark dark:text-white">
                      {operation.name}
                    </span>
                  </div>
                </td>
                <td className="py-3 px-2 text-center text-dark dark:text-white">
                  {operation.totalScans.toLocaleString()}
                </td>
                <td className="py-3 px-2 text-center text-dark dark:text-white">
                  {operation.uniquePackets.toLocaleString()}
                </td>
                <td className="py-3 px-2 text-center text-dark dark:text-white">
                  {operation.uniqueUsers}
                </td>
                <td className="py-3 px-2 text-center">
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-16 bg-gray-2 dark:bg-dark-3 rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full"
                        style={{ width: `${Math.min(operation.percentage, 100)}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-dark dark:text-white">
                      {operation.percentage.toFixed(1)}%
                    </span>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {(!Array.isArray(stats) || stats.length === 0) && (
        <div className="text-center py-8 text-body dark:text-dark-6">
          Aucune donnée d&apos;opération disponible
        </div>
      )}
    </div>
  );
}
