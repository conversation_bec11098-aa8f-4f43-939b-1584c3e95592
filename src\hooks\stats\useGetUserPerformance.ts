"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface UserPerformance {
  userId: string;
  userName: string;
  orderScans: {
    EM: number;
    SM: number;
    SF: number;
  };
  packetScans: {
    debutGM: number;
    finGM: number;
    ctrlFinCh: number;
    finFinition: number;
    operations: number;
  };
  totalScans: number;
  lastScanDate: string;
}

interface UseGetUserPerformanceParams {
  userId?: string;
  startDate?: string;
  endDate?: string;
}

export const useGetUserPerformance = ({ userId, startDate, endDate }: UseGetUserPerformanceParams = {}) => {
  const [data, setData] = useState<UserPerformance[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (userId) params.append('userId', userId);
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);

        const response = await axiosInstance.get(`/stats/user-performance?${params.toString()}`);

        // Get user names for each userId
        const userPerformanceData = response.data;
        const userIds = userPerformanceData.map((user: any) => user.userId);

        // Fetch user details to get names
        const userPromises = userIds.map((id: string) =>
          axiosInstance.get(`/users/${id}`).catch(() => ({ data: { name: id } }))
        );

        const userDetails = await Promise.all(userPromises);

        // Merge user names with performance data
        const enrichedData = userPerformanceData.map((user: any, index: number) => ({
          ...user,
          userName: userDetails[index]?.data?.name || user.userId
        }));

        setData(enrichedData);
      } catch (err: any) {
        console.error("Error fetching user performance:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des performances utilisateur");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchData();
  }, [userId, startDate, endDate, setLoading]);

  return { data, loading, error };
};
