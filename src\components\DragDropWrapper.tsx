"use client";

import { useEffect, useState } from "react";
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";

interface DragDropWrapperProps {
  children: React.ReactNode;
  onDragEnd: (result: any) => void;
}

export function DragDropWrapper({ children, onDragEnd }: DragDropWrapperProps) {
  const [isClient, setIsClient] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    // Simple source detection - assume from available-orders if not specified
    const sourceDroppableId = active.data.current?.sortable?.containerId || 'available-orders';

    // Convert dnd-kit event to react-beautiful-dnd format for compatibility
    const result = {
      draggableId: active.id as string,
      source: {
        droppableId: sourceDroppableId,
        index: 0,
      },
      destination: over.id === active.id ? null : {
        droppableId: over.id as string,
        index: 0,
      },
    };

    console.log('Drag end result:', result);
    onDragEnd(result);
  };

  if (!isClient) {
    return <div>{children}</div>;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {children}
    </DndContext>
  );
}
