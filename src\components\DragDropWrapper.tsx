"use client";

import { useEffect, useState } from "react";
import { DragDropContext } from "react-beautiful-dnd";

interface DragDropWrapperProps {
  children: React.ReactNode;
  onDragEnd: (result: any) => void;
}

export function DragDropWrapper({ children, onDragEnd }: DragDropWrapperProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <div>{children}</div>;
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      {children}
    </DragDropContext>
  );
}
