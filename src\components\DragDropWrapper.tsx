"use client";

import { useEffect, useState } from "react";
import {
  Dnd<PERSON>ontext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";

interface DragDropWrapperProps {
  children: React.ReactNode;
  onDragEnd: (result: any) => void;
}

export function DragDropWrapper({ children, onDragEnd }: DragDropWrapperProps) {
  const [isClient, setIsClient] = useState(false);
  const [activeId, setActiveId] = useState<string | null>(null);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (!over) return;

    // Determine source droppable
    let sourceDroppableId = 'available-orders';
    if (active.data.current?.sortable?.containerId) {
      sourceDroppableId = active.data.current.sortable.containerId;
    } else {
      // Check if the active item is in a day column
      const dayMatch = document.querySelector(`[data-order-id="${active.id}"]`);
      if (dayMatch) {
        const dayContainer = dayMatch.closest('[data-day-index]');
        if (dayContainer) {
          const dayIndex = dayContainer.getAttribute('data-day-index');
          sourceDroppableId = `day-${dayIndex}`;
        }
      }
    }

    // Convert dnd-kit event to react-beautiful-dnd format for compatibility
    const result = {
      draggableId: active.id as string,
      source: {
        droppableId: sourceDroppableId,
        index: 0,
      },
      destination: over.id === active.id ? null : {
        droppableId: over.id as string,
        index: 0,
      },
    };

    onDragEnd(result);
  };

  if (!isClient) {
    return <div>{children}</div>;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {children}
      <DragOverlay>
        {activeId ? (
          <div className="p-3 rounded-lg border bg-white shadow-lg rotate-2 scale-105">
            <div className="font-medium text-sm">Déplacement...</div>
          </div>
        ) : null}
      </DragOverlay>
    </DndContext>
  );
}
