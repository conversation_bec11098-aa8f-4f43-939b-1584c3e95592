"use client";

import { useState, useEffect } from "react";
import { Search, Package, Clock, AlertCircle } from "lucide-react";
import { useGetAvailableOrders } from "@/hooks/planning/useGetAvailableOrders";
import { Order } from "@/types/models";
import { Droppable } from "@/components/DragDrop/Droppable";
import { Draggable } from "@/components/DragDrop/Draggable";

export function AvailableOrdersList() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isClient, setIsClient] = useState(false);
  // Only fetch pending orders for planning
  const { orders, loading, error } = useGetAvailableOrders({ status: 'pending' });

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Filter orders that haven't started (no EM scan) and match search term
  const filteredOrders = orders.filter(order => {
    // Check if order has started (has EM scan)
    const hasStarted = order.scans && order.scans.some(scan => scan.type === 'EM');
    if (hasStarted) return false;

    // Apply search filter
    return order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (order.article?.ref && order.article.ref.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (order.article?.model && order.article.model.toLowerCase().includes(searchTerm.toLowerCase()));
  });

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'finnishing': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'En attente';
      case 'in_progress': return 'En cours';
      case 'finnishing': return 'Finition';
      case 'completed': return 'Terminé';
      default: return status;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card h-full flex flex-col">
      <div className="p-4 border-b border-stroke dark:border-dark-3 flex-shrink-0">
        <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
          Commandes à Planifier
        </h3>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-body dark:text-dark-6 w-4 h-4" />
          <input
            type="text"
            placeholder="Rechercher une commande..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-stroke dark:border-dark-3 rounded-lg bg-white dark:bg-gray-dark text-dark dark:text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
        </div>

        <div className="text-sm text-body dark:text-dark-6">
          {filteredOrders.length} commande{filteredOrders.length !== 1 ? 's' : ''} en attente
        </div>
        <div className="text-xs text-body dark:text-dark-6 mt-1">
          Glissez-déposez pour planifier
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <div className="p-4 h-full overflow-y-auto">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-body dark:text-dark-6 mt-2">Chargement...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
              <p className="text-sm text-red-500">{error}</p>
            </div>
        ) : !isClient ? (
          <div className="space-y-3 min-h-32">
            {filteredOrders.map((order) => (
              <div
                key={order._id}
                className={`p-3 rounded-lg border ${getStatusColor(order.status)}`}
              >
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="font-medium text-sm">
                      OF {order.orderNumber}
                    </div>
                    <div className="text-xs px-2 py-1 rounded-full bg-white/50">
                      {getStatusLabel(order.status)}
                    </div>
                  </div>

                  <div className="flex items-center gap-2 text-xs">
                    <Package className="w-3 h-3" />
                    <span>{order.totalPieces || 0} pièces</span>
                  </div>

                  {order.totalProductionTimeInMinutes > 0 && (
                    <div className="flex items-center gap-2 text-xs">
                      <Clock className="w-3 h-3" />
                      <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                    </div>
                  )}

                  {order.article && (
                    <div className="text-xs opacity-75">
                      {order.article.ref} - {order.article.model}
                    </div>
                  )}

                  {order.bloquer && (
                    <div className="flex items-center gap-1 text-xs text-red-600">
                      <AlertCircle className="w-3 h-3" />
                      <span>Bloqué</span>
                    </div>
                  )}

                  <div className="text-xs text-gray-500">
                    Créé le {new Date(order.createdAt).toLocaleDateString('fr-FR')}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <Droppable id="available-orders">
            {(provided, snapshot) => (
              <div
                ref={provided.ref}
                className={`space-y-3 min-h-32 ${
                  snapshot.isDraggingOver ? 'bg-gray-50 dark:bg-dark-2 rounded-lg p-2' : ''
                }`}
              >
                {filteredOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <Package className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-body dark:text-dark-6">
                      {searchTerm || statusFilter
                        ? "Aucune commande trouvée"
                        : "Aucune commande disponible"
                      }
                    </p>
                  </div>
                ) : (
                  filteredOrders.map((order, index) => (
                    <Draggable
                      key={order._id}
                      id={order._id.toString()}
                      sourceDroppableId="available-orders"
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.ref}
                          style={provided.style}
                          data-order-id={order._id}
                          {...provided.dragHandleProps}
                          className={`p-3 rounded-lg border cursor-move transition-all select-none ${
                            snapshot.isDragging
                              ? 'shadow-lg rotate-2 scale-105'
                              : 'hover:shadow-md'
                          } ${getStatusColor(order.status)}`}
                        >
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="font-medium text-sm">
                                OF {order.orderNumber}
                              </div>
                              <div className="text-xs px-2 py-1 rounded-full bg-white/50">
                                {getStatusLabel(order.status)}
                              </div>
                            </div>

                            <div className="flex items-center gap-2 text-xs">
                              <Package className="w-3 h-3" />
                              <span>{order.totalPieces || 0} pièces</span>
                            </div>

                            {order.totalProductionTimeInMinutes > 0 && (
                              <div className="flex items-center gap-2 text-xs">
                                <Clock className="w-3 h-3" />
                                <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                              </div>
                            )}

                            {order.article && (
                              <div className="text-xs opacity-75">
                                {order.article.ref} - {order.article.model}
                              </div>
                            )}

                            {order.bloquer && (
                              <div className="flex items-center gap-1 text-xs text-red-600">
                                <AlertCircle className="w-3 h-3" />
                                <span>Bloqué</span>
                              </div>
                            )}

                            <div className="text-xs text-gray-500">
                              Créé le {new Date(order.createdAt).toLocaleDateString('fr-FR')}
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))
                )}
              </div>
            )}
          </Droppable>
          )}
        </div>
      </div>
    </div>
  );
}
