"use client";

import { useState } from "react";
import { Droppable, Draggable } from "react-beautiful-dnd";
import { Search, Package, Clock, AlertCircle, Filter } from "lucide-react";
import { useGetAvailableOrders } from "@/hooks/planning/useGetAvailableOrders";
import { Order } from "@/types/models";

export function AvailableOrdersList() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const { orders, loading, error } = useGetAvailableOrders({ status: statusFilter });

  const filteredOrders = orders.filter(order => 
    order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (order.article?.ref && order.article.ref.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (order.article?.model && order.article.model.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'finnishing': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'En attente';
      case 'in_progress': return 'En cours';
      case 'finnishing': return 'Finition';
      case 'completed': return 'Terminé';
      default: return status;
    }
  };

  return (
    <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card">
      <div className="p-4 border-b border-stroke dark:border-dark-3">
        <h3 className="text-lg font-semibold text-dark dark:text-white mb-4">
          Commandes Disponibles
        </h3>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-body dark:text-dark-6 w-4 h-4" />
          <input
            type="text"
            placeholder="Rechercher une commande..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-stroke dark:border-dark-3 rounded-lg bg-white dark:bg-gray-dark text-dark dark:text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
          />
        </div>

        {/* Status Filter */}
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-body dark:text-dark-6 w-4 h-4" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-stroke dark:border-dark-3 rounded-lg bg-white dark:bg-gray-dark text-dark dark:text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
          >
            <option value="">Tous les statuts</option>
            <option value="pending">En attente</option>
            <option value="in_progress">En cours</option>
            <option value="finnishing">Finition</option>
            <option value="completed">Terminé</option>
          </select>
        </div>

        <div className="mt-3 text-sm text-body dark:text-dark-6">
          {filteredOrders.length} commande{filteredOrders.length !== 1 ? 's' : ''} disponible{filteredOrders.length !== 1 ? 's' : ''}
        </div>
      </div>

      <div className="p-4">
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-sm text-body dark:text-dark-6 mt-2">Chargement...</p>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-red-500">{error}</p>
          </div>
        ) : (
          <Droppable droppableId="available-orders">
            {(provided, snapshot) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className={`space-y-3 min-h-32 ${
                  snapshot.isDraggingOver ? 'bg-gray-50 dark:bg-dark-2 rounded-lg p-2' : ''
                }`}
              >
                {filteredOrders.length === 0 ? (
                  <div className="text-center py-8">
                    <Package className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-body dark:text-dark-6">
                      {searchTerm || statusFilter 
                        ? "Aucune commande trouvée" 
                        : "Aucune commande disponible"
                      }
                    </p>
                  </div>
                ) : (
                  filteredOrders.map((order, index) => (
                    <Draggable
                      key={order._id}
                      draggableId={order._id}
                      index={index}
                    >
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...provided.dragHandleProps}
                          className={`p-3 rounded-lg border cursor-move transition-all ${
                            snapshot.isDragging 
                              ? 'shadow-lg rotate-2 scale-105' 
                              : 'hover:shadow-md'
                          } ${getStatusColor(order.status)}`}
                        >
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <div className="font-medium text-sm">
                                OF {order.orderNumber}
                              </div>
                              <div className="text-xs px-2 py-1 rounded-full bg-white/50">
                                {getStatusLabel(order.status)}
                              </div>
                            </div>
                            
                            <div className="flex items-center gap-2 text-xs">
                              <Package className="w-3 h-3" />
                              <span>{order.totalPieces || 0} pièces</span>
                            </div>

                            {order.totalProductionTimeInMinutes > 0 && (
                              <div className="flex items-center gap-2 text-xs">
                                <Clock className="w-3 h-3" />
                                <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                              </div>
                            )}

                            {order.article && (
                              <div className="text-xs opacity-75">
                                {order.article.ref} - {order.article.model}
                              </div>
                            )}

                            {order.bloquer && (
                              <div className="flex items-center gap-1 text-xs text-red-600">
                                <AlertCircle className="w-3 h-3" />
                                <span>Bloqué</span>
                              </div>
                            )}

                            <div className="text-xs text-gray-500">
                              Créé le {new Date(order.createdAt).toLocaleDateString('fr-FR')}
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))
                )}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        )}
      </div>
    </div>
  );
}
