"use client";

import { PeriodPicker } from "@/components/period-picker";
import { standardFormat } from "@/lib/format-number";
import { cn } from "@/lib/utils";
import { ProductionOverviewChart } from "./chart";
import { useGetDailyProduction } from "@/hooks/stats/useGetDailyProduction";

type PropsType = {
  timeFrame?: string;
  className?: string;
};

export function ProductionOverview({
  timeFrame = "monthly",
  className,
}: PropsType) {
  const days = timeFrame === "weekly" ? 7 : timeFrame === "yearly" ? 365 : 30;
  const { data, loading, error } = useGetDailyProduction({ days });

  if (loading) {
    return (
      <div
        className={cn(
          "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Production Overview
          </h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="production_overview" />
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className={cn(
          "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Production Overview
          </h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="production_overview" />
        </div>
        <div className="flex items-center justify-center h-64 text-red-500">
          Erreur lors du chargement des données
        </div>
      </div>
    );
  }

  const totalOrdersCreated = data.reduce((acc, item) => acc + item.ordersCreated, 0);
  const totalOrdersCompleted = data.reduce((acc, item) => acc + item.ordersCompleted, 0);
  const totalPacketsCompleted = data.reduce((acc, item) => acc + item.packetsCompleted, 0);
  const totalScans = data.reduce((acc, item) => acc + item.totalScans, 0);

  return (
    <div
      className={cn(
        "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex flex-wrap items-center justify-between gap-4">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          Production Overview
        </h2>

        <PeriodPicker defaultValue={timeFrame} sectionKey="production_overview" />
      </div>

      <ProductionOverviewChart data={data} />

      <dl className="grid divide-stroke text-center dark:divide-dark-3 sm:grid-cols-4 sm:divide-x [&>div]:flex [&>div]:flex-col-reverse [&>div]:gap-1">
        <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(totalOrdersCreated)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Ordres Créés</dd>
        </div>

        <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(totalOrdersCompleted)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Ordres Terminés</dd>
        </div>

        <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(totalPacketsCompleted)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Paquets Terminés</dd>
        </div>

        <div>
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(totalScans)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Total Scans</dd>
        </div>
      </dl>
    </div>
  );
}
