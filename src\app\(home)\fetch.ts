import axiosInstance from "@/utils/axiosInstance";

export async function getOverviewData() {
  try {
    // Get current month stats
    const currentDate = new Date();
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);

    // Get previous month for comparison
    const startOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
    const endOfPrevMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 0);

    const [currentStats, prevStats] = await Promise.all([
      axiosInstance.get(`/api/stats/production?startDate=${startOfMonth.toISOString().split('T')[0]}&endDate=${endOfMonth.toISOString().split('T')[0]}`),
      axiosInstance.get(`/api/stats/production?startDate=${startOfPrevMonth.toISOString().split('T')[0]}&endDate=${endOfPrevMonth.toISOString().split('T')[0]}`)
    ]);

    const current = currentStats.data;
    const previous = prevStats.data;

    // Calculate growth rates
    const calculateGrowthRate = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0;
      return ((current - previous) / previous) * 100;
    };

    return {
      views: {
        value: current.productivity?.totalScans || 0,
        growthRate: calculateGrowthRate(
          current.productivity?.totalScans || 0,
          previous.productivity?.totalScans || 0
        ),
      },
      profit: {
        value: current.orders?.completed || 0,
        growthRate: calculateGrowthRate(
          current.orders?.completed || 0,
          previous.orders?.completed || 0
        ),
      },
      products: {
        value: current.packets?.total || 0,
        growthRate: calculateGrowthRate(
          current.packets?.total || 0,
          previous.packets?.total || 0
        ),
      },
      users: {
        value: current.pieces?.total || 0,
        growthRate: calculateGrowthRate(
          current.pieces?.total || 0,
          previous.pieces?.total || 0
        ),
      },
    };
  } catch (error) {
    console.error("Error fetching overview data:", error);
    // Fallback to default values
    return {
      views: { value: 0, growthRate: 0 },
      profit: { value: 0, growthRate: 0 },
      products: { value: 0, growthRate: 0 },
      users: { value: 0, growthRate: 0 },
    };
  }
}

export async function getChatsData() {
  // Fake delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return [
    {
      name: "Jacob Jones",
      profile: "/images/user/user-01.png",
      isActive: true,
      lastMessage: {
        content: "See you tomorrow at the meeting!",
        type: "text",
        timestamp: "2024-12-19T14:30:00Z",
        isRead: false,
      },
      unreadCount: 3,
    },
    {
      name: "Wilium Smith",
      profile: "/images/user/user-03.png",
      isActive: true,
      lastMessage: {
        content: "Thanks for the update",
        type: "text",
        timestamp: "2024-12-19T10:15:00Z",
        isRead: true,
      },
      unreadCount: 0,
    },
    {
      name: "Johurul Haque",
      profile: "/images/user/user-04.png",
      isActive: false,
      lastMessage: {
        content: "What's up?",
        type: "text",
        timestamp: "2024-12-19T10:15:00Z",
        isRead: true,
      },
      unreadCount: 0,
    },
    {
      name: "M. Chowdhury",
      profile: "/images/user/user-05.png",
      isActive: false,
      lastMessage: {
        content: "Where are you now?",
        type: "text",
        timestamp: "2024-12-19T10:15:00Z",
        isRead: true,
      },
      unreadCount: 2,
    },
    {
      name: "Akagami",
      profile: "/images/user/user-07.png",
      isActive: false,
      lastMessage: {
        content: "Hey, how are you?",
        type: "text",
        timestamp: "2024-12-19T10:15:00Z",
        isRead: true,
      },
      unreadCount: 0,
    },
  ];
}