"use client";

import { useState } from "react";
import { Calendar, X } from "lucide-react";

interface DateRangePickerProps {
  startDate?: string;
  endDate?: string;
  onDateChange: (startDate?: string, endDate?: string) => void;
  className?: string;
}

export function DateRangePicker({ startDate, endDate, onDateChange, className }: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempStartDate, setTempStartDate] = useState(startDate || "");
  const [tempEndDate, setTempEndDate] = useState(endDate || "");

  const handleApply = () => {
    console.log('Date range applied:', {
      startDate: tempStartDate || undefined,
      endDate: tempEndDate || undefined,
      startDateParsed: tempStartDate ? new Date(tempStartDate).toISOString() : undefined,
      endDateParsed: tempEndDate ? new Date(tempEndDate).toISOString() : undefined
    });
    onDateChange(tempStartDate || undefined, tempEndDate || undefined);
    setIsOpen(false);
  };

  const handleClear = () => {
    setTempStartDate("");
    setTempEndDate("");
    onDateChange(undefined, undefined);
    setIsOpen(false);
  };

  const getDisplayText = () => {
    if (!startDate && !endDate) {
      return "Toutes les données";
    }
    if (startDate && endDate) {
      return `${new Date(startDate).toLocaleDateString('fr-FR')} - ${new Date(endDate).toLocaleDateString('fr-FR')}`;
    }
    if (startDate) {
      return `Depuis ${new Date(startDate).toLocaleDateString('fr-FR')}`;
    }
    if (endDate) {
      return `Jusqu'au ${new Date(endDate).toLocaleDateString('fr-FR')}`;
    }
    return "Sélectionner une période";
  };

  const presetRanges = [
    {
      label: "Aujourd'hui",
      getValue: () => {
        const today = new Date().toISOString().split('T')[0];
        return { start: today, end: today };
      }
    },
    {
      label: "7 derniers jours",
      getValue: () => {
        const end = new Date().toISOString().split('T')[0];
        const start = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        return { start, end };
      }
    },
    {
      label: "30 derniers jours",
      getValue: () => {
        const end = new Date().toISOString().split('T')[0];
        const start = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
        return { start, end };
      }
    },
    {
      label: "Ce mois",
      getValue: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
        const end = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
        return { start, end };
      }
    },
    {
      label: "Mois dernier",
      getValue: () => {
        const now = new Date();
        const start = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().split('T')[0];
        const end = new Date(now.getFullYear(), now.getMonth(), 0).toISOString().split('T')[0];
        return { start, end };
      }
    }
  ];

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 rounded-lg text-sm font-medium text-dark dark:text-white hover:bg-gray-1 dark:hover:bg-dark-2 transition-colors"
      >
        <Calendar className="w-4 h-4" />
        <span>{getDisplayText()}</span>
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-80 bg-white dark:bg-gray-dark border border-stroke dark:border-dark-3 rounded-lg shadow-lg z-50">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-dark dark:text-white">Sélectionner une période</h3>
              <button
                onClick={() => setIsOpen(false)}
                className="text-body dark:text-dark-6 hover:text-dark dark:hover:text-white"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Preset Ranges */}
            <div className="mb-4">
              <div className="grid grid-cols-1 gap-2">
                {presetRanges.map((preset) => (
                  <button
                    key={preset.label}
                    onClick={() => {
                      const { start, end } = preset.getValue();
                      setTempStartDate(start);
                      setTempEndDate(end);
                    }}
                    className="text-left px-3 py-2 text-sm text-dark dark:text-white hover:bg-gray-1 dark:hover:bg-dark-2 rounded transition-colors"
                  >
                    {preset.label}
                  </button>
                ))}
                <button
                  onClick={() => {
                    setTempStartDate("");
                    setTempEndDate("");
                  }}
                  className="text-left px-3 py-2 text-sm text-primary hover:bg-primary/10 rounded transition-colors font-medium"
                >
                  Toutes les données
                </button>
              </div>
            </div>

            {/* Custom Date Inputs */}
            <div className="space-y-3 mb-4">
              <div>
                <label className="block text-xs font-medium text-body dark:text-dark-6 mb-1">
                  Date de début
                </label>
                <input
                  type="date"
                  value={tempStartDate}
                  onChange={(e) => setTempStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-stroke dark:border-dark-3 rounded bg-white dark:bg-gray-dark text-dark dark:text-white text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-body dark:text-dark-6 mb-1">
                  Date de fin
                </label>
                <input
                  type="date"
                  value={tempEndDate}
                  onChange={(e) => setTempEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-stroke dark:border-dark-3 rounded bg-white dark:bg-gray-dark text-dark dark:text-white text-sm"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <button
                onClick={handleClear}
                className="flex-1 px-3 py-2 text-sm text-body dark:text-dark-6 hover:text-dark dark:hover:text-white border border-stroke dark:border-dark-3 rounded transition-colors"
              >
                Effacer
              </button>
              <button
                onClick={handleApply}
                className="flex-1 px-3 py-2 text-sm bg-primary text-white rounded hover:bg-primary/90 transition-colors"
              >
                Appliquer
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
