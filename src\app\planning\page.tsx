"use client";

import { useState, useEffect } from "react";
import { ChevronLeft, ChevronRight, Plus, Calendar } from "lucide-react";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import { WeeklyPlanningView } from "./_components/WeeklyPlanningView";
import { AvailableOrdersList } from "./_components/AvailableOrdersList";
import { useGetPlannings } from "@/hooks/planning/useGetPlannings";
import { usePlanningActions } from "@/hooks/planning/usePlanningActions";
import { PlanningSemaine } from "@/types/models";

export default function PlanningPage() {
  const [currentWeekStart, setCurrentWeekStart] = useState<Date>(() => {
    const now = new Date();
    const startOfWeek = new Date(now);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfWeek.setDate(diff);
    startOfWeek.setHours(0, 0, 0, 0);
    return startOfWeek;
  });

  const [currentPlanning, setCurrentPlanning] = useState<PlanningSemaine | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  const { plannings, loading: loadingPlannings } = useGetPlannings({
    year: currentWeekStart.getFullYear(),
    month: currentWeekStart.getMonth() + 1
  });

  const { createCurrentWeekPlanning, createPlanning, loading: actionLoading } = usePlanningActions();

  // Find planning for current week
  useEffect(() => {
    const weekEnd = new Date(currentWeekStart);
    weekEnd.setDate(currentWeekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);

    const planning = plannings.find(p => {
      const planningStart = new Date(p.startDate);
      const planningEnd = new Date(p.endDate);
      return planningStart <= currentWeekStart && planningEnd >= weekEnd;
    });

    setCurrentPlanning(planning || null);
  }, [plannings, currentWeekStart]);

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newWeekStart = new Date(currentWeekStart);
    newWeekStart.setDate(currentWeekStart.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentWeekStart(newWeekStart);
  };

  const goToCurrentWeek = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
    startOfWeek.setDate(diff);
    startOfWeek.setHours(0, 0, 0, 0);
    setCurrentWeekStart(startOfWeek);
  };

  const handleCreatePlanning = async () => {
    try {
      const weekEnd = new Date(currentWeekStart);
      weekEnd.setDate(currentWeekStart.getDate() + 6);
      weekEnd.setHours(23, 59, 59, 999);

      await createPlanning(
        currentWeekStart.toISOString(),
        weekEnd.toISOString()
      );

      // Refresh plannings
      window.location.reload();
    } catch (error) {
      console.error("Error creating planning:", error);
    }
  };

  const formatWeekRange = (startDate: Date) => {
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);

    const startStr = startDate.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short'
    });
    const endStr = endDate.toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });

    return `${startStr} - ${endStr}`;
  };

  const isCurrentWeek = () => {
    const now = new Date();
    const startOfCurrentWeek = new Date(now);
    const day = startOfCurrentWeek.getDay();
    const diff = startOfCurrentWeek.getDate() - day + (day === 0 ? -6 : 1);
    startOfCurrentWeek.setDate(diff);
    startOfCurrentWeek.setHours(0, 0, 0, 0);

    return currentWeekStart.getTime() === startOfCurrentWeek.getTime();
  };

  return (
    <>
      <Breadcrumb pageName="Plan de Semaine" />

      <div className="flex flex-col lg:flex-row gap-6">
        {/* Main Planning View */}
        <div className="flex-1">
          {/* Week Navigation */}
          <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                <button
                  onClick={() => navigateWeek('prev')}
                  className="p-2 rounded-lg border border-stroke dark:border-dark-3 hover:bg-gray-1 dark:hover:bg-dark-2 transition-colors"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>

                <div className="text-center">
                  <h2 className="text-xl font-semibold text-dark dark:text-white">
                    {formatWeekRange(currentWeekStart)}
                  </h2>
                  <p className="text-sm text-body dark:text-dark-6">
                    Semaine {Math.ceil((currentWeekStart.getTime() - new Date(currentWeekStart.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}
                  </p>
                </div>

                <button
                  onClick={() => navigateWeek('next')}
                  className="p-2 rounded-lg border border-stroke dark:border-dark-3 hover:bg-gray-1 dark:hover:bg-dark-2 transition-colors"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>

              <div className="flex items-center gap-2">
                {!isCurrentWeek() && (
                  <button
                    onClick={goToCurrentWeek}
                    className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-dark-2 text-dark dark:text-white rounded-lg hover:bg-gray-200 dark:hover:bg-dark-3 transition-colors"
                  >
                    <Calendar className="w-4 h-4" />
                    Aujourd&apos;hui
                  </button>
                )}

                {!currentPlanning && (
                  <button
                    onClick={handleCreatePlanning}
                    disabled={actionLoading}
                    className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50"
                  >
                    <Plus className="w-4 h-4" />
                    Créer Planning
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Weekly Planning Grid */}
          {currentPlanning ? (
            <WeeklyPlanningView
              planning={currentPlanning}
              currentWeekStart={currentWeekStart}
            />
          ) : (
            <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card p-12 text-center">
              <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-dark dark:text-white mb-2">
                Aucun planning pour cette semaine
              </h3>
              <p className="text-body dark:text-dark-6 mb-6">
                Créez un planning pour commencer à organiser vos commandes.
              </p>
              <button
                onClick={handleCreatePlanning}
                disabled={actionLoading}
                className="flex items-center gap-2 px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 mx-auto"
              >
                <Plus className="w-5 h-5" />
                Créer Planning de la Semaine
              </button>
            </div>
          )}
        </div>

        {/* Available Orders Sidebar */}
        <div className="lg:w-80 h-full">
          <div className="h-[600px]">
            <AvailableOrdersList />
          </div>
        </div>
      </div>
    </>
  );
}
