"use client";

import { useDroppable } from "@dnd-kit/core";
import { ReactNode } from "react";

interface DroppableProps {
  id: string;
  children: (provided: { ref: (element: HTMLElement | null) => void }, snapshot: { isDraggingOver: boolean }) => ReactNode;
}

export function Droppable({ id, children }: DroppableProps) {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  return (
    <>
      {children(
        { ref: setNodeRef },
        { isDraggingOver: isOver }
      )}
    </>
  );
}
