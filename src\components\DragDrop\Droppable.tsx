"use client";

import { ReactNode, useState, useRef } from "react";
import { useDragDropContext } from "../DragDropWrapper";

interface DroppableProps {
  id: string;
  children: (provided: { ref: (element: HTMLElement | null) => void }, snapshot: { isDraggingOver: boolean }) => ReactNode;
}

export function Droppable({ id, children }: DroppableProps) {
  const [isDraggingOver, setIsDraggingOver] = useState(false);
  const { onDragEnd } = useDragDropContext();
  const ref = useRef<HTMLElement | null>(null);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDraggingOver(false);

    const dragData = e.dataTransfer.getData('text/plain');
    if (dragData) {
      const { draggableId, sourceDroppableId } = JSON.parse(dragData);

      const result = {
        draggableId,
        source: {
          droppableId: sourceDroppableId,
          index: 0,
        },
        destination: {
          droppableId: id,
          index: 0,
        },
      };

      onDragEnd(result);
    }
  };

  const setNodeRef = (element: HTMLElement | null) => {
    ref.current = element;
    if (element) {
      element.addEventListener('dragover', handleDragOver as any);
      element.addEventListener('dragleave', handleDragLeave as any);
      element.addEventListener('drop', handleDrop as any);
    }
  };

  return (
    <>
      {children(
        { ref: setNodeRef },
        { isDraggingOver }
      )}
    </>
  );
}
