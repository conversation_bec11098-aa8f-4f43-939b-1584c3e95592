"use client";

import { PeriodPicker } from "@/components/period-picker";
import { standardFormat } from "@/lib/format-number";
import { cn } from "@/lib/utils";
import { DefectsOverviewChart } from "./chart";
import { useGetDefectStats } from "@/hooks/stats/useGetDefectStats";

type PropsType = {
  timeFrame?: string;
  className?: string;
};

export function DefectsOverview({
  timeFrame = "monthly",
  className,
}: PropsType) {
  const currentDate = new Date();
  let startDate: string, endDate: string;

  if (timeFrame === "weekly") {
    const weekAgo = new Date(currentDate.getTime() - 7 * 24 * 60 * 60 * 1000);
    startDate = weekAgo.toISOString().split('T')[0];
    endDate = currentDate.toISOString().split('T')[0];
  } else if (timeFrame === "yearly") {
    startDate = new Date(currentDate.getFullYear(), 0, 1).toISOString().split('T')[0];
    endDate = new Date(currentDate.getFullYear(), 11, 31).toISOString().split('T')[0];
  } else {
    startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1).toISOString().split('T')[0];
    endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).toISOString().split('T')[0];
  }

  const { stats, loading, error } = useGetDefectStats({ startDate, endDate });

  if (loading) {
    return (
      <div
        className={cn(
          "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Analyse des Défauts
          </h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="defects_overview" />
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div
        className={cn(
          "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
          className,
        )}
      >
        <div className="flex flex-wrap items-center justify-between gap-4">
          <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
            Analyse des Défauts
          </h2>
          <PeriodPicker defaultValue={timeFrame} sectionKey="defects_overview" />
        </div>
        <div className="flex items-center justify-center h-64 text-red-500">
          Erreur lors du chargement des données
        </div>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "grid gap-2 rounded-[10px] bg-white px-7.5 pb-6 pt-7.5 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex flex-wrap items-center justify-between gap-4">
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          Analyse des Défauts
        </h2>

        <PeriodPicker defaultValue={timeFrame} sectionKey="defects_overview" />
      </div>

      <DefectsOverviewChart data={stats} />

      <dl className="grid divide-stroke text-center dark:divide-dark-3 sm:grid-cols-4 sm:divide-x [&>div]:flex [&>div]:flex-col-reverse [&>div]:gap-1">
        <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(stats.packetDefects.total)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Défauts Paquets</dd>
        </div>

        <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(stats.pieceDefects.total)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Défauts Pièces</dd>
        </div>

        <div className="dark:border-dark-3 max-sm:mb-3 max-sm:border-b max-sm:pb-3">
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(stats.blocked.orders + stats.blocked.packets)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Éléments Bloqués</dd>
        </div>

        <div>
          <dt className="text-xl font-bold text-dark dark:text-white">
            {standardFormat(stats.retouchePackets)}
          </dt>
          <dd className="font-medium dark:text-dark-6">Retouches</dd>
        </div>
      </dl>
    </div>
  );
}
