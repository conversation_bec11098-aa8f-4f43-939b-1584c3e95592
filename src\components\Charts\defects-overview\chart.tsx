"use client";

import { ApexOptions } from "apexcharts";
import dynamic from "next/dynamic";
import { useTheme } from "next-themes";

const ReactApexChart = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

interface DefectStats {
  packetDefects: {
    total: number;
    breakdown: Record<string, number>;
  };
  pieceDefects: {
    total: number;
    breakdown: Record<string, number>;
  };
  colisProblems: {
    total: number;
    breakdown: Record<string, number>;
  };
  blocked: {
    orders: number;
    packets: number;
  };
  retouchePackets: number;
}

interface DefectsOverviewChartProps {
  data: DefectStats;
}

export function DefectsOverviewChart({ data }: DefectsOverviewChartProps) {
  const { theme } = useTheme();

  // Prepare data for donut chart
  const chartData = [
    data.packetDefects.total,
    data.pieceDefects.total,
    data.colisProblems.total,
    data.blocked.orders + data.blocked.packets,
    data.retouchePackets
  ];

  const labels = [
    "Défauts Paquets",
    "Défauts Pièces", 
    "Problèmes Colis",
    "Éléments Bloqués",
    "Retouches"
  ];

  const options: ApexOptions = {
    chart: {
      type: "donut",
      height: 300,
      fontFamily: "Satoshi, sans-serif",
      background: "transparent",
    },
    colors: ["#FF6B6B", "#4ECDC4", "#45B7D1", "#FFA726", "#AB47BC"],
    labels: labels,
    dataLabels: {
      enabled: true,
      style: {
        fontSize: "12px",
        colors: [theme === "dark" ? "#FFFFFF" : "#000000"],
      },
    },
    legend: {
      position: "bottom",
      horizontalAlign: "center",
      labels: {
        colors: theme === "dark" ? "#9CA3AF" : "#6B7280",
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: "60%",
          labels: {
            show: true,
            total: {
              show: true,
              label: "Total",
              fontSize: "16px",
              fontWeight: "bold",
              color: theme === "dark" ? "#FFFFFF" : "#000000",
              formatter: function (w) {
                return w.globals.seriesTotals.reduce((a: number, b: number) => a + b, 0).toString();
              }
            }
          }
        }
      }
    },
    tooltip: {
      theme: theme === "dark" ? "dark" : "light",
      y: {
        formatter: function (val) {
          return val.toString();
        }
      }
    },
    responsive: [
      {
        breakpoint: 480,
        options: {
          chart: {
            height: 250,
          },
          legend: {
            position: "bottom",
          },
        },
      },
    ],
  };

  return (
    <div className="h-[300px]">
      <ReactApexChart
        options={options}
        series={chartData}
        type="donut"
        height={300}
      />
    </div>
  );
}
