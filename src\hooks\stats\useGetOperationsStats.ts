"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface OperationStats {
  operation: string;
  totalScans: number;
  uniquePackets: number;
  uniqueUsers: number;
  percentage: number;
}

interface UseGetOperationsStatsParams {
  startDate?: string;
  endDate?: string;
}

export const useGetOperationsStats = ({ startDate, endDate }: UseGetOperationsStatsParams = {}) => {
  const [stats, setStats] = useState<OperationStats[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);

        const response = await axiosInstance.get(`/stats/operations?${params.toString()}`);
        setStats(response.data);
      } catch (err: any) {
        console.error("Error fetching operations stats:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des statistiques d'opérations");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchStats();
  }, [startDate, endDate, setLoading]);

  return { stats, loading, error };
};
