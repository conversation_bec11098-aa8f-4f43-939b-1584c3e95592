"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface OperationStats {
  name: string;
  totalScans: number;
  uniquePackets: number;
  uniqueUsers: number;
  percentage: string | number; // Server returns string with .toFixed(2)
}

interface OperationsResponse {
  totalOperationScans: number;
  operations: OperationStats[];
}

interface UseGetOperationsStatsParams {
  startDate?: string;
  endDate?: string;
}

export const useGetOperationsStats = ({ startDate, endDate }: UseGetOperationsStatsParams = {}) => {
  const [stats, setStats] = useState<OperationStats[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);

        const response = await axiosInstance.get<OperationsResponse>(`/stats/operations?${params.toString()}`);
        // Extract the operations array from the response
        setStats(response.data.operations || []);
      } catch (err: any) {
        console.error("Error fetching operations stats:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des statistiques d'opérations");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchStats();
  }, [startDate, endDate, setLoading]);

  return { stats, loading, error };
};
