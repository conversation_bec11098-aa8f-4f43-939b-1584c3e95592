"use client";

import { useState, useEffect, useContext } from "react";
import { LoadingContext } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface OperationStats {
  operation: string;
  totalScans: number;
  uniquePackets: number;
  uniqueUsers: number;
  percentage: number;
}

interface UseGetOperationsStatsParams {
  startDate?: string;
  endDate?: string;
}

export const useGetOperationsStats = ({ startDate, endDate }: UseGetOperationsStatsParams = {}) => {
  const [stats, setStats] = useState<OperationStats[]>([]);
  const [loading2, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useContext(LoadingContext);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading2(true);
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (startDate) params.append('startDate', startDate);
        if (endDate) params.append('endDate', endDate);

        const response = await axiosInstance.get(`/api/stats/operations?${params.toString()}`);
        setStats(response.data);
      } catch (err: any) {
        console.error("Error fetching operations stats:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des statistiques d'opérations");
      } finally {
        setLoading2(false);
        setLoading(false);
      }
    };

    fetchStats();
  }, [startDate, endDate, setLoading]);

  return { stats, loading: loading2, error };
};
