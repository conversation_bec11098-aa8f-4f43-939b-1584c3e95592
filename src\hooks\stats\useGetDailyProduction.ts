"use client";

import { useState, useEffect, useContext } from "react";
import { LoadingContext } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface DailyProductionData {
  date: string;
  ordersCreated: number;
  ordersCompleted: number;
  packetsCompleted: number;
  totalScans: number;
}

interface UseGetDailyProductionParams {
  days?: number;
}

export const useGetDailyProduction = ({ days = 30 }: UseGetDailyProductionParams = {}) => {
  const [data, setData] = useState<DailyProductionData[]>([]);
  const [loading2, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useContext(LoadingContext);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading2(true);
        setLoading(true);
        setError(null);

        const response = await axiosInstance.get(`/api/stats/daily-production?days=${days}`);
        setData(response.data);
      } catch (err: any) {
        console.error("Error fetching daily production:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des données quotidiennes");
      } finally {
        setLoading2(false);
        setLoading(false);
      }
    };

    fetchData();
  }, [days, setLoading]);

  return { data, loading: loading2, error };
};
