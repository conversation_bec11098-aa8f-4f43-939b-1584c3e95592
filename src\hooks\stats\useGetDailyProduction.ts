"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface DailyProductionData {
  date: string;
  ordersCreated: number;
  ordersCompleted: number;
  packetsCompleted: number;
  totalScans: number;
}

interface UseGetDailyProductionParams {
  days?: number;
  startDate?: string;
  endDate?: string;
}

export const useGetDailyProduction = ({ days = 30, startDate, endDate }: UseGetDailyProductionParams = {}) => {
  const [data, setData] = useState<DailyProductionData[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        // For daily production, we need to get enough days to cover the date range
        // Since the server endpoint doesn't support startDate/endDate, we'll get more data and filter client-side
        let requestDays = days;

        if (startDate || endDate) {
          if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end.getTime() - start.getTime());
            requestDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
          } else if (startDate) {
            // If only start date, get from start date to today
            const start = new Date(startDate);
            const today = new Date();
            const diffTime = Math.abs(today.getTime() - start.getTime());
            requestDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
          } else {
            // If only end date, get last 365 days and filter
            requestDays = 365;
          }
        }

        console.log('Daily production request:', {
          startDate,
          endDate,
          requestDays,
          url: `/stats/daily-production?days=${requestDays}`
        });

        const response = await axiosInstance.get(`/stats/daily-production?days=${requestDays}`);

        let filteredData = response.data;

        // Filter data by date range if provided
        if (startDate || endDate) {
          filteredData = response.data.filter((item: DailyProductionData) => {
            const itemDate = new Date(item.date);
            if (startDate && itemDate < new Date(startDate)) return false;
            if (endDate && itemDate > new Date(endDate)) return false;
            return true;
          });
        }

        console.log('Daily production filtered data:', filteredData.length, 'items');
        setData(filteredData);
      } catch (err: any) {
        console.error("Error fetching daily production:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des données quotidiennes");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchData();
  }, [days, startDate, endDate, setLoading]);

  return { data, loading, error };
};
