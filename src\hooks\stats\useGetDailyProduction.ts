"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface DailyProductionData {
  date: string;
  ordersCreated: number;
  ordersCompleted: number;
  packetsCompleted: number;
  totalScans: number;
}

interface UseGetDailyProductionParams {
  days?: number;
  startDate?: string;
  endDate?: string;
}

export const useGetDailyProduction = ({ days = 30, startDate, endDate }: UseGetDailyProductionParams = {}) => {
  const [data, setData] = useState<DailyProductionData[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const params = new URLSearchParams();
        if (startDate || endDate) {
          // If specific dates are provided, calculate days between them
          if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            const diffTime = Math.abs(end.getTime() - start.getTime());
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
            params.append('days', diffDays.toString());
          } else {
            params.append('days', days.toString());
          }
        } else {
          params.append('days', days.toString());
        }

        const response = await axiosInstance.get(`/stats/daily-production?${params.toString()}`);

        let filteredData = response.data;

        // Filter data by date range if provided
        if (startDate || endDate) {
          filteredData = response.data.filter((item: DailyProductionData) => {
            const itemDate = new Date(item.date);
            if (startDate && itemDate < new Date(startDate)) return false;
            if (endDate && itemDate > new Date(endDate)) return false;
            return true;
          });
        }

        setData(filteredData);
      } catch (err: any) {
        console.error("Error fetching daily production:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des données quotidiennes");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchData();
  }, [days, startDate, endDate, setLoading]);

  return { data, loading, error };
};
