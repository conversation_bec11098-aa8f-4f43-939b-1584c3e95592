"use client";

import { useState, useEffect } from "react";
import { useLoading } from "@/context/LoadingContext";
import axiosInstance from "@/utils/axiosInstance";

interface DailyProductionData {
  date: string;
  ordersCreated: number;
  ordersCompleted: number;
  packetsCompleted: number;
  totalScans: number;
}

interface UseGetDailyProductionParams {
  days?: number;
}

export const useGetDailyProduction = ({ days = 30 }: UseGetDailyProductionParams = {}) => {
  const [data, setData] = useState<DailyProductionData[]>([]);
  const [loading, setLoading2] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setLoading } = useLoading();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setLoading2(true);
        setError(null);

        const response = await axiosInstance.get(`/stats/daily-production?days=${days}`);
        setData(response.data);
      } catch (err: any) {
        console.error("Error fetching daily production:", err);
        setError(err.response?.data?.message || "Erreur lors de la récupération des données quotidiennes");
      } finally {
        setLoading(false);
        setLoading2(false);
      }
    };

    fetchData();
  }, [days, setLoading]);

  return { data, loading, error };
};
