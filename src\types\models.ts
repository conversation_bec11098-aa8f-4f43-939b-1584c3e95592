// Common types for models used throughout the application

export interface Piece {
  _id: string;
  numero: number;
  status: string;
  defaut: string[];
}

export interface Packet {
  _id: string;
  numero: number;
  size: string;
  status: string;
  bloquer: boolean; // Indicates if the packet is blocked
  colis: string | Colis; // Reference to parent Colis
  pieces: Piece[] | string[];
  qrCode: string;
  scans: Scan[];
}

export interface Colis {
  _id: string;
  numeroColis: number;
  coloris: string;
  tailles: string;
  quantite: number;
  status: string;
  problems: string[];
  control?: 'Conforme' | 'ANC' | 'Bloque'; // Control status
  order: string | Order; // Reference to parent Order
  packets: Packet[] | string[];
  qrCode: string;
  scans: Scan[];
}

export interface Order {
  _id: string;
  orderNumber: string;
  totalProductionTimeInMinutes: number;
  status: string;
  bloquer: boolean; // Indicates if the order is blocked
  launchDate?: string; // Date when the order is planned to be launched
  colis: Colis[] | string[]; // References to Colis
  qrCode: string;
  chaine: string;
  totalPieces: number;
  scans: Scan[];
  article: any; // This should be more specific if possible
  createdAt: string;
  updatedAt: string;
}

export interface Scan {
  type: string;
  time: string;
  user?: string;
}

// Data structures for creating new entities

export interface ColisData {
  numeroColis: number;
  coloris: string;
  tailles: string;
  quantite: number;
  piecesPerPacket: number;
}

export interface CreateOrderData {
  orderNumber: string;
  chaine: string;
  totalPieces: number;
  colisData: ColisData[];
  article: string;
}

// Planning types
export interface PlanningDay {
  date: string;
  dayName: string;
  orders: Order[];
}

export interface PlanningSemaine {
  _id: string;
  startDate: string;
  endDate: string;
  days: PlanningDay[];
  weekNumber?: number;
  createdAt: string;
  updatedAt: string;
}

export interface PlanningStats {
  totalOrders: number;
  totalPieces: number;
  dailyBreakdown: {
    dayIndex: number;
    date: string;
    dayName: string;
    ordersCount: number;
    totalPieces: number;
    estimatedTime: number;
  }[];
  ordersByStatus: {
    pending: number;
    in_progress: number;
    finnishing: number;
    completed: number;
  };
}
