"use client";

import { useState, useEffect } from "react";
import { Clock, Package, AlertCircle } from "lucide-react";
import { PlanningSemaine, Order } from "@/types/models";
import { usePlanningActions } from "@/hooks/planning/usePlanningActions";
import { useGetAvailableOrders } from "@/hooks/planning/useGetAvailableOrders";
import { DragDropWrapper } from "@/components/DragDropWrapper";
import { Droppable } from "@/components/DragDrop/Droppable";
import { Draggable } from "@/components/DragDrop/Draggable";

interface WeeklyPlanningViewProps {
  planning: PlanningSemaine;
  currentWeekStart: Date;
}

export function WeeklyPlanningView({ planning, currentWeekStart }: WeeklyPlanningViewProps) {
  const { moveOrder, assignOrders, removeOrders } = usePlanningActions();
  const { refetch: refetchAvailableOrders } = useGetAvailableOrders();
  const [draggedOrder, setDraggedOrder] = useState<Order | null>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const isToday = (date: string) => {
    const today = new Date();
    const dayDate = new Date(date);
    return dayDate.toDateString() === today.toDateString();
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'in_progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'finnishing': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'completed': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleDragEnd = async (result: any) => {
    const { destination, source, draggableId } = result;

    if (!destination) return;

    // If dropped in the same position, do nothing
    if (destination.droppableId === source.droppableId && destination.index === source.index) {
      return;
    }

    try {
      // Handle moving from sidebar to day
      if (source.droppableId === 'available-orders') {
        const dayIndex = parseInt(destination.droppableId.replace('day-', ''));
        await assignOrders(planning._id, dayIndex, [draggableId]);
        refetchAvailableOrders();
        window.location.reload(); // Refresh to get updated planning
        return;
      }

      // Handle moving from day to sidebar (remove from planning)
      if (destination.droppableId === 'available-orders') {
        const dayIndex = parseInt(source.droppableId.replace('day-', ''));
        await removeOrders(planning._id, dayIndex, [draggableId]);
        refetchAvailableOrders();
        window.location.reload(); // Refresh to get updated planning
        return;
      }

      // Handle moving between days
      const fromDayIndex = parseInt(source.droppableId.replace('day-', ''));
      const toDayIndex = parseInt(destination.droppableId.replace('day-', ''));

      if (fromDayIndex !== toDayIndex) {
        await moveOrder(planning._id, draggableId, fromDayIndex, toDayIndex);
        window.location.reload(); // Refresh to get updated planning
      }
    } catch (error) {
      console.error("Error handling drag and drop:", error);
    }
  };

  const calculateDayStats = (orders: Order[]) => {
    const totalOrders = orders.length;
    const totalPieces = orders.reduce((sum, order) => sum + (order.totalPieces || 0), 0);
    const totalTime = orders.reduce((sum, order) => sum + (order.totalProductionTimeInMinutes || 0), 0);

    return { totalOrders, totalPieces, totalTime };
  };

  // Static version for server-side rendering
  if (!isClient) {
    return (
      <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card overflow-hidden">
        {/* Week Header */}
        <div className="grid grid-cols-7 border-b border-stroke dark:border-dark-3">
          {planning.days.map((day, index) => {
            const stats = calculateDayStats(day.orders);
            const dayDate = new Date(day.date);
            const isCurrentDay = isToday(day.date);

            return (
              <div
                key={index}
                className={`p-4 text-center border-r border-stroke dark:border-dark-3 last:border-r-0 ${
                  isCurrentDay ? 'bg-primary/10 dark:bg-primary/20' : ''
                }`}
              >
                <div className="font-semibold text-dark dark:text-white">
                  {day.dayName}
                </div>
                <div className={`text-sm ${isCurrentDay ? 'text-primary font-medium' : 'text-body dark:text-dark-6'}`}>
                  {dayDate.getDate()}/{dayDate.getMonth() + 1}
                </div>

                {/* Day Stats */}
                <div className="mt-2 space-y-1">
                  <div className="text-xs text-body dark:text-dark-6">
                    {stats.totalOrders} commande{stats.totalOrders !== 1 ? 's' : ''}
                  </div>
                  {stats.totalPieces > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {stats.totalPieces} pièces
                    </div>
                  )}
                  {stats.totalTime > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {formatTime(stats.totalTime)}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Week Content - Static version */}
        <div className="grid grid-cols-7 min-h-96">
          {planning.days.map((day, dayIndex) => (
            <div
              key={dayIndex}
              className={`p-3 border-r border-stroke dark:border-dark-3 last:border-r-0 min-h-96 ${
                isToday(day.date) ? 'bg-primary/5 dark:bg-primary/10' : ''
              }`}
            >
              <div className="space-y-2">
                {day.orders.map((order) => (
                  <div
                    key={order._id}
                    className={`p-3 rounded-lg border ${getStatusColor(order.status)}`}
                  >
                    <div className="space-y-2">
                      <div className="font-medium text-sm">
                        OF {order.orderNumber}
                      </div>

                      <div className="flex items-center gap-2 text-xs">
                        <Package className="w-3 h-3" />
                        <span>{order.totalPieces || 0} pièces</span>
                      </div>

                      {order.totalProductionTimeInMinutes > 0 && (
                        <div className="flex items-center gap-2 text-xs">
                          <Clock className="w-3 h-3" />
                          <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                        </div>
                      )}

                      {order.article && (
                        <div className="text-xs opacity-75">
                          {order.article.ref} - {order.article.model}
                        </div>
                      )}

                      {order.bloquer && (
                        <div className="flex items-center gap-1 text-xs text-red-600">
                          <AlertCircle className="w-3 h-3" />
                          <span>Bloqué</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <DragDropWrapper onDragEnd={handleDragEnd}>
      <div className="bg-white dark:bg-gray-dark rounded-lg shadow-1 dark:shadow-card overflow-hidden">
        {/* Week Header */}
        <div className="grid grid-cols-7 border-b border-stroke dark:border-dark-3">
          {planning.days.map((day, index) => {
            const stats = calculateDayStats(day.orders);
            const dayDate = new Date(day.date);
            const isCurrentDay = isToday(day.date);

            return (
              <div
                key={index}
                className={`p-4 text-center border-r border-stroke dark:border-dark-3 last:border-r-0 ${
                  isCurrentDay ? 'bg-primary/10 dark:bg-primary/20' : ''
                }`}
              >
                <div className="font-semibold text-dark dark:text-white">
                  {day.dayName}
                </div>
                <div className={`text-sm ${isCurrentDay ? 'text-primary font-medium' : 'text-body dark:text-dark-6'}`}>
                  {dayDate.getDate()}/{dayDate.getMonth() + 1}
                </div>

                {/* Day Stats */}
                <div className="mt-2 space-y-1">
                  <div className="text-xs text-body dark:text-dark-6">
                    {stats.totalOrders} commande{stats.totalOrders !== 1 ? 's' : ''}
                  </div>
                  {stats.totalPieces > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {stats.totalPieces} pièces
                    </div>
                  )}
                  {stats.totalTime > 0 && (
                    <div className="text-xs text-body dark:text-dark-6">
                      {formatTime(stats.totalTime)}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Week Content */}
        <div className="grid grid-cols-7 min-h-96">
          {planning.days.map((day, dayIndex) => (
            <Droppable key={dayIndex} id={`day-${dayIndex}`}>
              {(provided, snapshot) => (
                <div
                  ref={provided.ref}
                  className={`p-3 border-r border-stroke dark:border-dark-3 last:border-r-0 min-h-96 ${
                    snapshot.isDraggingOver ? 'bg-primary/5 dark:bg-primary/10' : ''
                  } ${isToday(day.date) ? 'bg-primary/5 dark:bg-primary/10' : ''}`}
                >
                  <div className="space-y-2">
                    {day.orders.map((order, orderIndex) => (
                      <Draggable
                        key={order._id}
                        id={order._id}
                      >
                        {(provided, snapshot) => (
                          <div
                            ref={provided.ref}
                            style={provided.style}
                            {...provided}
                            className={`p-3 rounded-lg border cursor-move transition-all ${
                              snapshot.isDragging
                                ? 'shadow-lg rotate-2 scale-105'
                                : 'hover:shadow-md'
                            } ${getStatusColor(order.status)}`}
                          >
                            <div className="space-y-2">
                              <div className="font-medium text-sm">
                                OF {order.orderNumber}
                              </div>

                              <div className="flex items-center gap-2 text-xs">
                                <Package className="w-3 h-3" />
                                <span>{order.totalPieces || 0} pièces</span>
                              </div>

                              {order.totalProductionTimeInMinutes > 0 && (
                                <div className="flex items-center gap-2 text-xs">
                                  <Clock className="w-3 h-3" />
                                  <span>{formatTime(order.totalProductionTimeInMinutes)}</span>
                                </div>
                              )}

                              {order.article && (
                                <div className="text-xs opacity-75">
                                  {order.article.ref} - {order.article.model}
                                </div>
                              )}

                              {order.bloquer && (
                                <div className="flex items-center gap-1 text-xs text-red-600">
                                  <AlertCircle className="w-3 h-3" />
                                  <span>Bloqué</span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </Draggable>
                    ))}
                  </div>

                  {/* Drop Zone Indicator */}
                  {snapshot.isDraggingOver && day.orders.length === 0 && (
                    <div className="border-2 border-dashed border-primary/50 rounded-lg p-6 text-center text-primary/70">
                      <Package className="w-8 h-8 mx-auto mb-2" />
                      <div className="text-sm">Déposer la commande ici</div>
                    </div>
                  )}
                </div>
              )}
            </Droppable>
          ))}
        </div>
      </div>
    </DragDropWrapper>
  );
}
