import { ProductionOverview } from "@/components/Charts/production-overview";
import { ManufacturingOverview } from "@/components/Manufacturing/ManufacturingOverview";
import { DefectsAnalysis } from "@/components/Manufacturing/DefectsAnalysis";
import { OperationsPerformance } from "@/components/Tables/operations-performance";
import { UserPerformanceDetailed } from "@/components/Manufacturing/UserPerformanceDetailed";
import { createTimeFrameExtractor } from "@/utils/timeframe-extractor";
import { Suspense } from "react";
import { OverviewCardsGroup } from "./_components/overview-cards";
import { OverviewCardsSkeleton } from "./_components/overview-cards/skeleton";

type PropsType = {
  searchParams: Promise<{
    selected_time_frame?: string;
  }>;
};

export default async function Home({ searchParams }: PropsType) {
  const { selected_time_frame } = await searchParams;
  const extractTimeFrame = createTimeFrameExtractor(selected_time_frame);

  return (
    <>
      <Suspense fallback={<OverviewCardsSkeleton />}>
        <OverviewCardsGroup />
      </Suspense>

      {/* Manufacturing Overview - Full Width */}
      <div className="mt-4 md:mt-6 2xl:mt-9">
        <ManufacturingOverview className="w-full" />
      </div>

      {/* Production Charts and Analytics */}
      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        <ProductionOverview
          className="col-span-12 xl:col-span-8"
          key={extractTimeFrame("production_overview")}
          timeFrame={extractTimeFrame("production_overview")?.split(":")[1]}
        />

        <DefectsAnalysis
          key={extractTimeFrame("defects_analysis")}
          timeFrame={extractTimeFrame("defects_analysis")?.split(":")[1]}
          className="col-span-12 xl:col-span-4"
        />

        <OperationsPerformance
          className="col-span-12"
          key={extractTimeFrame("operations_performance")}
          timeFrame={extractTimeFrame("operations_performance")?.split(":")[1]}
        />

        <UserPerformanceDetailed
          className="col-span-12"
          key={extractTimeFrame("user_performance_detailed")}
          timeFrame={extractTimeFrame("user_performance_detailed")?.split(":")[1]}
        />
      </div>
    </>
  );
}
