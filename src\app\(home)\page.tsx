"use client";

import { ProductionOverview } from "@/components/Charts/production-overview";
import { ManufacturingOverview } from "@/components/Manufacturing/ManufacturingOverview";
import { DefectsAnalysis } from "@/components/Manufacturing/DefectsAnalysis";
import { OperationsPerformance } from "@/components/Tables/operations-performance";
import { UserPerformanceDetailed } from "@/components/Manufacturing/UserPerformanceDetailed";
import { DateRangePicker } from "@/components/DateRangePicker";
import { createTimeFrameExtractor } from "@/utils/timeframe-extractor";
import { useState } from "react";
import { OverviewCardsWithFilter } from "./_components/overview-cards-with-filter";

export default function Home() {
  const [dateRange, setDateRange] = useState<{ startDate?: string; endDate?: string }>({});

  const handleDateChange = (startDate?: string, endDate?: string) => {
    setDateRange({ startDate, endDate });
  };

  return (
    <>
      {/* Date Filter Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div>
          <h1 className="text-2xl font-bold text-dark dark:text-white">Tableau de Bord Production</h1>
          <p className="text-body dark:text-dark-6">Vue d'ensemble de votre activité de fabrication</p>
        </div>
        <DateRangePicker
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
          onDateChange={handleDateChange}
        />
      </div>

      {/* Overview Cards with Date Filter */}
      <OverviewCardsWithFilter
        startDate={dateRange.startDate}
        endDate={dateRange.endDate}
      />

      {/* Manufacturing Overview - Full Width */}
      <div className="mt-4 md:mt-6 2xl:mt-9">
        <ManufacturingOverview
          className="w-full"
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
        />
      </div>

      {/* Production Charts and Analytics */}
      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        <ProductionOverview
          className="col-span-12 xl:col-span-8"
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
        />

        <DefectsAnalysis
          className="col-span-12 xl:col-span-4"
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
        />

        <OperationsPerformance
          className="col-span-12"
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
        />

        <UserPerformanceDetailed
          className="col-span-12"
          startDate={dateRange.startDate}
          endDate={dateRange.endDate}
        />
      </div>
    </>
  );
}
