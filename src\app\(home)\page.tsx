import { ProductionOverview } from "@/components/Charts/production-overview";
import { DefectsOverview } from "@/components/Charts/defects-overview";
import { WeeksProfit } from "@/components/Charts/weeks-profit";
import { OperationsPerformance } from "@/components/Tables/operations-performance";
import { UserPerformance } from "@/components/Tables/user-performance";
import { createTimeFrameExtractor } from "@/utils/timeframe-extractor";
import { Suspense } from "react";
import { ChatsCard } from "./_components/chats-card";
import { OverviewCardsGroup } from "./_components/overview-cards";
import { OverviewCardsSkeleton } from "./_components/overview-cards/skeleton";
import { RegionLabels } from "./_components/region-labels";

type PropsType = {
  searchParams: Promise<{
    selected_time_frame?: string;
  }>;
};

export default async function Home({ searchParams }: PropsType) {
  const { selected_time_frame } = await searchParams;
  const extractTimeFrame = createTimeFrameExtractor(selected_time_frame);

  return (
    <>
      <Suspense fallback={<OverviewCardsSkeleton />}>
        <OverviewCardsGroup />
      </Suspense>

      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        <ProductionOverview
          className="col-span-12 xl:col-span-7"
          key={extractTimeFrame("production_overview")}
          timeFrame={extractTimeFrame("production_overview")?.split(":")[1]}
        />

        <DefectsOverview
          key={extractTimeFrame("defects_overview")}
          timeFrame={extractTimeFrame("defects_overview")?.split(":")[1]}
          className="col-span-12 xl:col-span-5"
        />

        <OperationsPerformance
          className="col-span-12 xl:col-span-7"
          key={extractTimeFrame("operations_performance")}
          timeFrame={extractTimeFrame("operations_performance")?.split(":")[1]}
        />

        <UserPerformance
          className="col-span-12 xl:col-span-5"
          key={extractTimeFrame("user_performance")}
          timeFrame={extractTimeFrame("user_performance")?.split(":")[1]}
        />

        <Suspense fallback={null}>
          <ChatsCard />
        </Suspense>
      </div>
    </>
  );
}
