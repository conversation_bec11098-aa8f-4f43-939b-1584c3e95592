"use client";

import { useDraggable } from "@dnd-kit/core";
import { ReactNode } from "react";

interface DraggableProps {
  id: string;
  children: (
    provided: {
      ref: (element: HTMLElement | null) => void;
      style?: React.CSSProperties;
      [key: string]: any;
    },
    snapshot: { isDragging: boolean }
  ) => ReactNode;
}

export function Draggable({ id, children }: DraggableProps) {
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id,
  });

  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      }
    : undefined;

  return (
    <>
      {children(
        {
          ref: setNodeRef,
          style,
          ...listeners,
          ...attributes,
        },
        { isDragging }
      )}
    </>
  );
}
